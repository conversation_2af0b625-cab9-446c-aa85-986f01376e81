import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../features/auth/service/auth_service.dart';
import '../utils/getDeviceID.dart';
import 'custom_exception.dart';

class DataPostRequests {
  static const String portalUrl = 'https://api.nudron.com/prod/portal';
  static const String nf1Url = '$portalUrl/nf1';
  static const String nf3Url = '$portalUrl/nf3';
  static const String wm1Url = 'https://api.nudron.com/prod/dashboard/wm1';
  static FlutterSecureStorage secureStorage = const FlutterSecureStorage();

  static getIconNamesForAlerts(int index) {
    switch (index) {
      case 0:
        return "Empty Pipe";
      case 1:
        return "No Consumption";
      case 2:
        return "Reverse Flow";
      case 3:
        return "Leak Flow";
      case 4:
        return "Continuos Flow";
      case 5:
        return "Burst Pipe";
      case 6:
        return "Max Flow";
      case 7:
        return "Freeze";
      default:
        return "RFU";
    }
  }

  static getIconNamesForStatus(int index) {
    switch (index) {
      case 1:
        return "Low Bat";
      case 2:
        return "Bad Temp";
      case 3:
        return "Motion";
      case 7:
        return "Air in Pipe";
      default:
        return "RFU";
    }
  }

  static Future<void> printJson(Map<dynamic, dynamic> data, int number) async {
    // Convert map to JSON string
    String jsonString = jsonEncode(data);

    // Check if the JSON string is too long for the console
    if (jsonString.length > 1000) {
      // Print in chunks
      const int chunkSize = 1000;
      for (int i = 0; i < jsonString.length; i += chunkSize) {
        print(jsonString.substring(
            i,
            i + chunkSize > jsonString.length
                ? jsonString.length
                : i + chunkSize));
      }
    } else {
      // Print directly if the string is short enough
      print(jsonString);
    }

    // Dummy wait for 2 seconds
    await Future.delayed(const Duration(seconds: 2));
  }

  static getDummyFilters() async {
    await Future.delayed(const Duration(seconds: 2));
    return '''[ [ "Trends", [ "Building", "Flat", "Device" ], { "Building": { "402": [ "Main" ], "3rd Floor": [ "Main Line", "Main" ], "4th Floor": [ "Main Line", "Main" ], "Flat": [ "Flush Line", "Flush", "Domestic Line", "Main" ], "Basement": [ "Main" ], "Basement Floor": [ "Main Line" ], "Ground Floor": [ "Main", "Main Line" ] }, "Gitaneel Arcawwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwde22": { "1st Floor": [ "Main" ], "3rd Floor": [ "Main Line", "Main" ], "4th Floor": [ "Main Line", "Main" ], "Flat": [ "Flush Line", "Flush", "Domestic Line", "Main" ], "Basement": [ "Main" ], "Basement Floor": [ "Main Line" ], "Ground Floor": [ "Main", "Main Line" ] } } ], [ "Billing", "200<10:15<20:25<30:40<50" ], ["Devices",[
            [
                "Label   ",
                "Serial #",
                "Model",
                "%Last Seen",
                "Totalizer",
                "@Last Record",
                "Usage(L)",
                "Alerts",
                "!Al0",
                "!Al1",
                "!Al2",
                "!Al3",
                "!Al4",
                "!Al5",
                "!Al6",
                "!Al7",
                "!St1",
                "!St2",
                "!St3",
                "!St7",
                "US:00-02",
                "US:02-04",
                "US:04-06",
                "US:06-08",
                "US:08-10",
                "US:10-12",
                "US:12-14",
                "US:14-16",
                "US:16-18",
                "US:18-20",
                "US:20-22",
                "US:22-24"
            ],
            [
                [
                    "Gitaneel Arcade>5&6 Floor>Main",
                    "101100000248",
                    "NuFM-B-25W",
                    1740385683000,
                    344388,
                    1880,
                    1703,
                    3,
                    0,
                    0,
                    1,
                    1,
                    1,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    6,
                    6,
                    4,
                    55,
                    33,
                    317,
                    258,
                    319,
                    240,
                    281,
                    175,
                    9
                ],
                [
                    "Gitaneel Arcade>Ground Floor>Main",
                    "101100000235",
                    "NuFM-B-15W",
                    1740386249000,
                    484038,
                    1880,
                    1508,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    11,
                    0,
                    0,
                    139,
                    29,
                    198,
                    177,
                    247,
                    182,
                    218,
                    201,
                    106
                ],
                [
                    "Gitaneel Arcade>1st Floor>Main",
                    "101100000251",
                    "NuFM-B-25W",
                    1740341675000,
                    395770,
                    1880,
                    1994,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    130,
                    215,
                    151,
                    233,
                    62,
                    136,
                    327,
                    151,
                    178,
                    124,
                    94,
                    193
                ],
                [
                    "Gitaneel Arcade>4th Floor>Main",
                    "101100000247",
                    "NuFM-B-25W",
                    1577836800000,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                ],
                [
                    "Gitaneel Arcade>Basement>Main",
                    "101100000238",
                    "NuFM-B-15W",
                    1740365360000,
                    187405,
                    1880,
                    1561,
                    1,
                    0,
                    0,
                    0,
                    0,
                    1,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    16,
                    8,
                    6,
                    160,
                    646,
                    143,
                    60,
                    256,
                    145,
                    67,
                    41,
                    13
                ],
                [
                    "Gitaneel Arcade>5&6 Floor>Flush",
                    "101100000246",
                    "NuFM-B-25W",
                    1740386080000,
                    305404,
                    1880,
                    761,
                    1,
                    0,
                    0,
                    1,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    92,
                    73,
                    91,
                    147,
                    28,
                    106,
                    142,
                    75,
                    7
                ],
                [
                    "Gitaneel Arcade>3rd Floor>Main",
                    "101100000249",
                    "NuFM-B-25W",
                    1740393510000,
                    49608,
                    1880,
                    255,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    36,
                    3,
                    57,
                    15,
                    39,
                    23,
                    57,
                    25,
                    0
                ]
            ]
        ]]]''';
  }

  static getDummyChartData() async {
    await Future.delayed(const Duration(seconds: 1));
    return '''[["Date","Usage"," #AL ","!Al0","!Al1","!Al2","!Al3","!Al4","!Al5","!Al6","!Al7"," #ST ","!St1","!St2","!St3","!St7","US:00-02","US:02-04","US:04-06","US:06-08","US:08-10","US:10-12","US:12-14","US:14-16","US:16-18","US:18-20","US:20-22","US:22-24"],[[1780,7076,5,0,0,2,1,2,0,0,0,0,0,0,0,0,89,101,185,556,857,1128,1175,1449,1396,1210,639,146]]]''';
    return '''[["Date", "Usage", "#AL", "!Al0", "!Al1", "!Al2", "!Al3", "!Al4", "!Al5", "!Al6", "!Al7", "#ST", "!St1", "!St2", "!St3", "!St7"], [[1696, 17, 3, 0, 0, 1, 0, 1, 1, 0, 0, 9, 2, 3, 4, 0], [1695, 23, 1, 0, 0, 0, 0, 0, 1, 0, 0, 6, 0, 3, 1, 2], [1694, 19, 1, 0, 0, 0, 0, 0, 0, 1, 0, 10, 2, 1, 4, 3], [1693, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1692, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 2, 1, 3], [1691, 17, 2, 0, 0, 0, 0, 1, 0, 1, 0, 9, 4, 0, 2, 3], [1690, 17, 1, 0, 1, 0, 0, 0, 0, 0, 0, 6, 2, 4, 0, 0], [1689, 7, 2, 0, 1, 1, 0, 0, 0, 0, 0, 10, 5, 2, 1, 2], [1688, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 0, 0, 0], [1687, 12, 3, 0, 1, 0, 0, 0, 0, 2, 0, 9, 2, 3, 4, 0], [1686, 14, 2, 0, 1, 1, 0, 0, 0, 0, 0, 5, 0, 0, 3, 2], [1685, 5, 2, 0, 0, 0, 0, 1, 0, 1, 0, 10, 1, 2, 3, 4], [1684, 23, 3, 0, 0, 1, 1, 0, 0, 0, 1, 5, 2, 2, 1, 0], [1683, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 0, 3, 1], [1682, 19, 3, 0, 0, 0, 1, 0, 1, 1, 0, 9, 3, 3, 1, 2], [1681, 5, 1, 0, 0, 0, 0, 0, 1, 0, 0, 7, 1, 1, 3, 2], [1680, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1679, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1678, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 2, 1, 0], [1677, 14, 1, 0, 0, 0, 0, 0, 0, 1, 0, 6, 2, 2, 1, 1], [1676, 18, 1, 1, 0, 0, 0, 0, 0, 0, 0, 8, 1, 4, 2, 1], [1675, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 1, 3, 3], [1674, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 1, 1, 0], [1673, 16, 1, 0, 0, 0, 1, 0, 0, 0, 0, 6, 2, 2, 2, 0], [1672, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 3, 3, 2], [1671, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 1, 5], [1670, 19, 1, 0, 0, 1, 0, 0, 0, 0, 0, 8, 1, 0, 5, 2], [1669, 22, 1, 0, 0, 0, 0, 1, 0, 0, 0, 5, 1, 0, 1, 3], [1668, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 1, 2, 1], [1667, 19, 2, 0, 0, 0, 0, 1, 1, 0, 0, 5, 1, 1, 1, 2], [1666, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1665, 17, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0], [1664, 7, 1, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 0, 2, 0], [1663, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 2, 1, 0], [1662, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 2, 0, 0], [1661, 7, 3, 0, 1, 1, 0, 0, 0, 1, 0, 5, 0, 0, 1, 4], [1660, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 4, 2, 3], [1659, 16, 1, 0, 0, 1, 0, 0, 0, 0, 0, 8, 2, 3, 1, 2], [1658, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 1, 0, 0], [1657, 23, 1, 0, 0, 0, 0, 0, 1, 0, 0, 4, 1, 1, 1, 1], [1656, 12, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0], [1655, 14, 2, 0, 0, 1, 0, 0, 0, 1, 0, 3, 0, 1, 0, 2], [1654, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 4, 2, 0, 4], [1653, 20, 1, 0, 0, 1, 0, 0, 0, 0, 0, 3, 0, 1, 1, 1], [1652, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 1, 0, 1], [1651, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2], [1650, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 1, 2, 2], [1649, 23, 2, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0], [1648, 12, 2, 0, 0, 1, 0, 0, 0, 1, 0, 2, 1, 1, 0, 0], [1647, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 2, 2, 2], [1646, 10, 1, 0, 0, 1, 0, 0, 0, 0, 0, 10, 1, 4, 2, 3], [1645, 20, 1, 0, 0, 0, 0, 0, 0, 0, 1, 3, 0, 1, 2, 0], [1644, 18, 2, 2, 0, 0, 0, 0, 0, 0, 0, 9, 2, 2, 1, 4], [1643, 12, 2, 0, 0, 1, 0, 0, 0, 0, 1, 3, 2, 0, 0, 1], [1642, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 3, 1], [1641, 18, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 2, 0, 0], [1640, 5, 1, 0, 1, 0, 0, 0, 0, 0, 0, 4, 0, 1, 3, 0], [1639, 23, 1, 0, 0, 1, 0, 0, 0, 0, 0, 9, 2, 0, 1, 6], [1638, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1637, 10, 1, 0, 0, 1, 0, 0, 0, 0, 0, 4, 0, 0, 1, 3], [1636, 12, 3, 1, 0, 0, 0, 1, 0, 1, 0, 10, 0, 5, 1, 4], [1635, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 1, 3, 0], [1634, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1633, 12, 2, 0, 0, 1, 0, 0, 0, 1, 0, 7, 2, 4, 0, 1], [1632, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1631, 24, 3, 0, 0, 2, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1630, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 0, 2, 2], [1629, 14, 1, 0, 0, 0, 0, 0, 0, 0, 1, 10, 3, 3, 1, 3], [1628, 22, 2, 1, 0, 0, 0, 0, 0, 1, 0, 7, 5, 0, 2, 0], [1627, 18, 1, 0, 0, 0, 0, 0, 1, 0, 0, 10, 1, 2, 4, 3], [1626, 7, 3, 0, 0, 1, 0, 1, 0, 1, 0, 10, 4, 2, 1, 3], [1625, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2], [1624, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1623, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0], [1622, 10, 2, 1, 0, 0, 0, 0, 0, 1, 0, 6, 0, 3, 1, 2], [1621, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 2, 1, 4, 2], [1620, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 1, 1], [1619, 21, 3, 0, 1, 1, 0, 1, 0, 0, 0, 10, 2, 1, 4, 3], [1618, 6, 1, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 5, 4], [1617, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 0, 2, 3], [1616, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 2, 0, 1], [1615, 12, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1614, 10, 3, 1, 1, 1, 0, 0, 0, 0, 0, 2, 2, 0, 0, 0], [1613, 13, 1, 0, 1, 0, 0, 0, 0, 0, 0, 5, 1, 2, 2, 0], [1612, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 0, 1], [1611, 5, 2, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0], [1610, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 3, 0, 0], [1609, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 2, 0], [1608, 8, 3, 1, 0, 0, 0, 1, 0, 0, 1, 10, 3, 3, 4, 0], [1607, 17, 2, 1, 0, 1, 0, 0, 0, 0, 0, 6, 1, 0, 3, 2], [1606, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 1, 0], [1605, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 0, 1, 4], [1604, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 0, 4], [1603, 9, 1, 0, 0, 0, 0, 0, 1, 0, 0, 8, 2, 3, 3, 0], [1602, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 0, 1], [1601, 18, 1, 1, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1600, 18, 1, 1, 0, 0, 0, 0, 0, 0, 0, 10, 3, 2, 1, 4], [1599, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 0, 2, 3], [1598, 9, 2, 0, 1, 0, 0, 0, 0, 0, 1, 6, 3, 3, 0, 0], [1597, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 0, 3], [1596, 16, 1, 0, 0, 0, 0, 0, 0, 1, 0, 9, 2, 1, 0, 6], [1595, 11, 1, 0, 0, 0, 1, 0, 0, 0, 0, 9, 0, 3, 3, 3], [1594, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 4, 3, 3], [1593, 13, 3, 1, 1, 0, 1, 0, 0, 0, 0, 6, 2, 3, 1, 0], [1592, 10, 1, 0, 0, 0, 0, 1, 0, 0, 0, 4, 2, 0, 2, 0], [1591, 7, 3, 0, 0, 1, 0, 1, 1, 0, 0, 8, 0, 1, 5, 2], [1590, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 0, 0, 0], [1589, 20, 2, 0, 0, 0, 0, 0, 0, 0, 2, 10, 2, 1, 4, 3], [1588, 10, 1, 0, 1, 0, 0, 0, 0, 0, 0, 9, 1, 3, 3, 2], [1587, 17, 2, 0, 0, 0, 1, 0, 1, 0, 0, 3, 1, 1, 0, 1], [1586, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 3, 0, 1], [1585, 20, 1, 0, 0, 0, 0, 0, 0, 1, 0, 9, 0, 2, 3, 4], [1584, 9, 3, 0, 0, 0, 0, 0, 0, 2, 1, 1, 1, 0, 0, 0], [1583, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 2, 2, 1], [1582, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 4, 1, 1, 1], [1581, 11, 1, 0, 0, 0, 0, 1, 0, 0, 0, 8, 1, 1, 5, 1], [1580, 15, 2, 1, 0, 0, 1, 0, 0, 0, 0, 8, 3, 3, 0, 2], [1579, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1578, 9, 2, 0, 0, 0, 0, 1, 1, 0, 0, 10, 3, 3, 2, 2], [1577, 12, 3, 1, 0, 0, 0, 1, 0, 0, 1, 6, 0, 1, 2, 3], [1576, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0], [1575, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1574, 8, 2, 1, 0, 0, 0, 0, 1, 0, 0, 5, 1, 2, 1, 1], [1573, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1572, 20, 1, 1, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 1, 3], [1571, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 3, 2, 0], [1570, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1569, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 0, 0, 0], [1568, 15, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1567, 17, 1, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 0, 2, 0], [1566, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 4, 2, 3], [1565, 21, 3, 0, 0, 0, 0, 0, 2, 0, 1, 6, 1, 1, 3, 1], [1564, 5, 1, 0, 0, 0, 0, 0, 1, 0, 0, 5, 1, 1, 1, 2], [1563, 17, 1, 0, 0, 0, 0, 0, 0, 1, 0, 10, 2, 2, 6, 0], [1562, 16, 1, 0, 0, 1, 0, 0, 0, 0, 0, 4, 1, 0, 2, 1], [1561, 11, 1, 0, 0, 0, 0, 0, 1, 0, 0, 4, 0, 3, 1, 0], [1560, 10, 2, 1, 0, 0, 0, 1, 0, 0, 0, 6, 1, 0, 1, 4], [1559, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 3, 2, 1], [1558, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 4, 1, 3], [1557, 6, 3, 0, 0, 0, 1, 0, 1, 1, 0, 6, 2, 1, 2, 1], [1556, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 2, 0, 3], [1555, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 2, 2, 2], [1554, 8, 2, 0, 1, 0, 0, 0, 0, 1, 0, 4, 0, 3, 1, 0], [1553, 20, 3, 1, 0, 0, 0, 0, 2, 0, 0, 6, 2, 2, 2, 0], [1552, 19, 1, 0, 0, 0, 0, 0, 0, 1, 0, 10, 3, 2, 3, 2], [1551, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 3, 2, 3], [1550, 8, 1, 0, 0, 1, 0, 0, 0, 0, 0, 3, 1, 0, 0, 2], [1549, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 2, 2, 1], [1548, 15, 1, 0, 0, 1, 0, 0, 0, 0, 0, 7, 1, 3, 1, 2], [1547, 24, 2, 0, 0, 0, 1, 0, 1, 0, 0, 2, 1, 1, 0, 0], [1546, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 4, 3, 0, 3], [1545, 8, 1, 0, 1, 0, 0, 0, 0, 0, 0, 9, 3, 4, 2, 0], [1544, 19, 3, 0, 1, 0, 0, 0, 1, 1, 0, 2, 0, 0, 2, 0], [1543, 6, 3, 1, 0, 2, 0, 0, 0, 0, 0, 7, 4, 1, 1, 1], [1542, 19, 3, 2, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0], [1541, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 5, 3, 1, 1], [1540, 17, 2, 1, 0, 0, 0, 0, 0, 0, 1, 6, 1, 1, 3, 1], [1539, 24, 1, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 2, 3, 4], [1538, 24, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1537, 5, 3, 0, 1, 0, 0, 0, 1, 1, 0, 3, 1, 0, 1, 1], [1536, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 1, 4, 0, 3], [1535, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 1, 2, 2], [1534, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4, 0, 1, 1], [1533, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 2, 3, 0, 4], [1532, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0], [1531, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 2, 2, 3, 2], [1530, 24, 1, 0, 1, 0, 0, 0, 0, 0, 0, 4, 1, 1, 2, 0], [1529, 13, 2, 1, 0, 0, 0, 0, 0, 1, 0, 10, 3, 1, 3, 3], [1528, 16, 2, 0, 0, 0, 1, 0, 0, 1, 0, 9, 1, 1, 4, 3], [1527, 19, 3, 0, 0, 0, 0, 0, 0, 2, 1, 5, 2, 1, 1, 1], [1526, 15, 3, 1, 1, 0, 1, 0, 0, 0, 0, 5, 4, 1, 0, 0], [1525, 14, 2, 1, 1, 0, 0, 0, 0, 0, 0, 2, 1, 0, 0, 1], [1524, 9, 1, 0, 1, 0, 0, 0, 0, 0, 0, 6, 2, 1, 3, 0], [1523, 21, 1, 0, 0, 0, 0, 0, 1, 0, 0, 6, 1, 3, 1, 1], [1522, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 1, 0], [1521, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 1, 2], [1520, 14, 1, 0, 0, 0, 0, 0, 0, 0, 1, 9, 2, 2, 3, 2], [1519, 5, 3, 0, 0, 1, 0, 1, 0, 0, 1, 4, 0, 0, 1, 3], [1518, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 1, 2, 1], [1517, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 2, 2, 2], [1516, 7, 2, 0, 0, 0, 0, 1, 0, 0, 1, 7, 3, 2, 1, 1], [1515, 10, 3, 1, 0, 1, 0, 0, 1, 0, 0, 7, 1, 2, 2, 2], [1514, 19, 2, 0, 0, 0, 0, 0, 0, 1, 1, 10, 3, 3, 3, 1], [1513, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1512, 8, 2, 0, 0, 0, 0, 1, 1, 0, 0, 6, 3, 2, 1, 0], [1511, 8, 2, 0, 0, 0, 0, 1, 0, 0, 1, 5, 3, 0, 1, 1], [1510, 16, 1, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 1, 1], [1509, 18, 2, 0, 0, 0, 0, 1, 0, 0, 1, 10, 4, 2, 2, 2], [1508, 9, 2, 0, 0, 1, 0, 0, 0, 1, 0, 9, 3, 4, 0, 2], [1507, 7, 3, 2, 1, 0, 0, 0, 0, 0, 0, 9, 5, 1, 2, 1], [1506, 8, 3, 1, 1, 0, 0, 0, 0, 1, 0, 4, 1, 1, 2, 0], [1505, 12, 3, 0, 1, 0, 1, 0, 0, 1, 0, 7, 0, 4, 1, 2], [1504, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 1, 0], [1503, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 2, 2, 4], [1502, 10, 1, 0, 0, 0, 0, 0, 1, 0, 0, 3, 0, 1, 1, 1], [1501, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0], [1500, 10, 3, 1, 0, 0, 0, 1, 0, 0, 1, 2, 1, 1, 0, 0], [1499, 5, 2, 0, 0, 0, 1, 0, 0, 0, 1, 6, 2, 1, 0, 3], [1498, 12, 3, 1, 0, 0, 2, 0, 0, 0, 0, 8, 4, 3, 0, 1], [1497, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 1, 1, 0], [1496, 5, 3, 1, 0, 1, 1, 0, 0, 0, 0, 5, 1, 3, 0, 1], [1495, 13, 2, 0, 1, 0, 0, 0, 0, 1, 0, 3, 1, 0, 0, 2], [1494, 14, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 2], [1493, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 5, 1, 1, 1], [1492, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 2, 0, 3], [1491, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 2, 2, 1], [1490, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0], [1489, 20, 2, 1, 0, 0, 0, 0, 0, 0, 1, 8, 0, 0, 3, 5], [1488, 11, 2, 0, 0, 0, 0, 1, 0, 0, 1, 6, 2, 1, 1, 2], [1487, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 1, 2, 3], [1486, 5, 1, 0, 0, 0, 0, 1, 0, 0, 0, 9, 0, 3, 1, 5], [1485, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 2, 3, 3], [1484, 7, 3, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0], [1483, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 0, 1, 1], [1482, 5, 2, 0, 1, 0, 0, 1, 0, 0, 0, 2, 0, 1, 1, 0], [1481, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 0, 1, 2], [1480, 10, 1, 0, 0, 0, 0, 1, 0, 0, 0, 5, 0, 1, 1, 3], [1479, 16, 2, 0, 1, 0, 1, 0, 0, 0, 0, 3, 1, 0, 2, 0], [1478, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 3, 0, 4], [1477, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 5, 1, 1, 2], [1476, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 1, 0], [1475, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 1, 0], [1474, 19, 1, 0, 0, 0, 1, 0, 0, 0, 0, 4, 1, 1, 1, 1], [1473, 20, 1, 0, 0, 0, 1, 0, 0, 0, 0, 3, 2, 0, 0, 1], [1472, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 2, 0, 1], [1471, 18, 3, 0, 1, 0, 1, 0, 1, 0, 0, 9, 2, 0, 5, 2], [1470, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 4, 1, 1, 4], [1469, 6, 1, 0, 0, 1, 0, 0, 0, 0, 0, 3, 2, 1, 0, 0], [1468, 7, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0], [1467, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 5, 1, 1], [1466, 11, 2, 0, 0, 0, 0, 0, 1, 1, 0, 2, 1, 0, 0, 1], [1465, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 2, 2, 4], [1464, 13, 3, 0, 0, 0, 0, 0, 1, 1, 1, 6, 0, 0, 1, 5], [1463, 19, 2, 0, 0, 0, 0, 0, 1, 1, 0, 5, 2, 1, 0, 2], [1462, 22, 1, 0, 0, 0, 0, 0, 1, 0, 0, 10, 0, 5, 4, 1], [1461, 13, 1, 0, 1, 0, 0, 0, 0, 0, 0, 5, 0, 1, 1, 3], [1460, 10, 2, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 1], [1459, 24, 3, 0, 0, 1, 0, 0, 1, 0, 1, 3, 1, 1, 1, 0], [1458, 11, 2, 1, 0, 0, 0, 1, 0, 0, 0, 7, 1, 2, 3, 1], [1457, 11, 3, 0, 1, 0, 0, 1, 0, 0, 1, 9, 2, 1, 4, 2], [1456, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 2, 1], [1455, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1454, 18, 3, 0, 0, 2, 1, 0, 0, 0, 0, 6, 0, 2, 4, 0], [1453, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 4, 1, 2, 1], [1452, 15, 2, 0, 0, 0, 0, 0, 1, 1, 0, 3, 1, 1, 1, 0], [1451, 11, 3, 0, 0, 1, 0, 2, 0, 0, 0, 8, 2, 1, 3, 2], [1450, 11, 2, 0, 0, 0, 0, 0, 0, 0, 2, 5, 1, 1, 1, 2], [1449, 16, 1, 0, 0, 0, 0, 0, 1, 0, 0, 8, 4, 0, 1, 3], [1448, 20, 3, 0, 1, 1, 0, 1, 0, 0, 0, 10, 1, 2, 2, 5], [1447, 15, 2, 0, 0, 0, 0, 1, 0, 0, 1, 9, 1, 2, 3, 3], [1446, 22, 2, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1], [1445, 8, 2, 0, 0, 0, 1, 1, 0, 0, 0, 3, 1, 0, 0, 2], [1444, 16, 3, 1, 0, 0, 0, 0, 0, 1, 1, 2, 2, 0, 0, 0], [1443, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 1, 1], [1442, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 0, 1, 2], [1441, 10, 1, 0, 0, 0, 0, 0, 0, 1, 0, 10, 4, 2, 2, 2], [1440, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 2, 3, 0], [1439, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 1, 1], [1438, 21, 2, 0, 0, 0, 2, 0, 0, 0, 0, 5, 2, 1, 1, 1], [1437, 15, 3, 2, 0, 1, 0, 0, 0, 0, 0, 8, 1, 2, 3, 2], [1436, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 0, 3], [1435, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 0, 2, 0], [1434, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 1, 3, 1], [1433, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 2, 0, 1, 0], [1432, 8, 2, 0, 1, 0, 1, 0, 0, 0, 0, 4, 2, 1, 0, 1], [1431, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 1, 2, 1], [1430, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 1, 2], [1429, 9, 2, 0, 0, 0, 0, 0, 0, 0, 2, 3, 1, 0, 1, 1], [1428, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0], [1427, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 1, 1, 1], [1426, 22, 1, 0, 1, 0, 0, 0, 0, 0, 0, 5, 0, 2, 2, 1], [1425, 16, 1, 0, 0, 0, 1, 0, 0, 0, 0, 5, 1, 1, 1, 2], [1424, 10, 1, 0, 0, 1, 0, 0, 0, 0, 0, 10, 2, 1, 2, 5], [1423, 6, 3, 0, 0, 0, 0, 1, 0, 1, 1, 8, 0, 0, 4, 4], [1422, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 0, 3, 0], [1421, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 3, 4, 2], [1420, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 1, 0, 0], [1419, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 4, 4, 0, 0], [1418, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 5, 0, 1], [1417, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 1, 3, 2], [1416, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 3, 2, 1], [1415, 18, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1414, 12, 1, 0, 1, 0, 0, 0, 0, 0, 0, 4, 2, 1, 1, 0], [1413, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 3, 0, 1], [1412, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 1, 1, 1], [1411, 10, 2, 1, 1, 0, 0, 0, 0, 0, 0, 6, 2, 1, 1, 2], [1410, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 1, 3, 4], [1409, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 1, 0, 2], [1408, 12, 2, 0, 1, 0, 0, 0, 1, 0, 0, 3, 0, 0, 3, 0], [1407, 11, 1, 0, 0, 0, 0, 0, 1, 0, 0, 9, 3, 0, 1, 5], [1406, 9, 2, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0], [1405, 14, 3, 0, 0, 0, 0, 1, 0, 2, 0, 3, 1, 2, 0, 0], [1404, 16, 2, 0, 0, 1, 0, 0, 0, 1, 0, 10, 5, 1, 1, 3], [1403, 17, 1, 0, 0, 0, 0, 0, 0, 0, 1, 8, 1, 2, 4, 1], [1402, 9, 3, 0, 2, 1, 0, 0, 0, 0, 0, 4, 1, 1, 0, 2], [1401, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 1, 4, 1], [1400, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 2, 0], [1399, 5, 1, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 0, 1, 2], [1398, 7, 3, 0, 0, 0, 2, 0, 0, 1, 0, 10, 2, 2, 0, 6], [1397, 12, 2, 0, 1, 0, 0, 1, 0, 0, 0, 8, 1, 2, 4, 1], [1396, 6, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1395, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 2, 1], [1394, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1393, 19, 1, 0, 0, 0, 0, 0, 0, 1, 0, 9, 2, 2, 2, 3], [1392, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 4, 0, 3], [1391, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1390, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1389, 9, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1388, 6, 1, 0, 0, 0, 1, 0, 0, 0, 0, 5, 2, 1, 1, 1], [1387, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0], [1386, 22, 3, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0], [1385, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 0, 0, 0], [1384, 10, 1, 0, 0, 0, 0, 0, 0, 0, 1, 8, 1, 1, 4, 2], [1383, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 2, 2], [1382, 8, 1, 0, 1, 0, 0, 0, 0, 0, 0, 7, 1, 2, 2, 2], [1381, 23, 2, 0, 0, 0, 2, 0, 0, 0, 0, 4, 1, 0, 2, 1], [1380, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 1, 1, 2], [1379, 6, 3, 1, 1, 0, 0, 0, 0, 0, 1, 5, 2, 0, 1, 2], [1378, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 1, 0], [1377, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 2, 0, 1, 0], [1376, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1, 2, 2], [1375, 12, 3, 0, 1, 0, 0, 0, 0, 1, 1, 6, 1, 1, 3, 1], [1374, 9, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0], [1373, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 1, 1, 4, 2], [1372, 12, 3, 0, 0, 1, 1, 0, 1, 0, 0, 9, 2, 2, 5, 0], [1371, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 1, 2, 0], [1370, 22, 3, 1, 0, 0, 1, 1, 0, 0, 0, 4, 0, 1, 1, 2], [1369, 14, 1, 0, 0, 1, 0, 0, 0, 0, 0, 3, 1, 1, 0, 1], [1368, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 2, 2, 1], [1367, 16, 1, 0, 0, 1, 0, 0, 0, 0, 0, 2, 0, 0, 1, 1], [1366, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 2, 2, 1], [1365, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4, 0, 1, 1], [1364, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 2, 3, 3], [1363, 21, 1, 1, 0, 0, 0, 0, 0, 0, 0, 7, 2, 1, 3, 1], [1362, 7, 3, 1, 1, 0, 0, 0, 1, 0, 0, 7, 0, 3, 1, 3], [1361, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 2, 0, 2], [1360, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 3, 2], [1359, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 4, 2, 1, 0], [1358, 8, 3, 0, 1, 1, 0, 0, 0, 1, 0, 7, 2, 3, 1, 1], [1357, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 1, 4, 1], [1356, 21, 2, 0, 0, 0, 0, 0, 1, 1, 0, 7, 3, 3, 1, 0], [1355, 17, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0], [1354, 12, 1, 0, 0, 0, 1, 0, 0, 0, 0, 9, 3, 2, 1, 3], [1353, 5, 3, 2, 0, 0, 0, 0, 0, 1, 0, 4, 0, 2, 1, 1], [1352, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 2, 0, 0, 1], [1351, 12, 1, 0, 0, 0, 0, 0, 1, 0, 0, 7, 1, 1, 1, 4], [1350, 14, 1, 0, 0, 1, 0, 0, 0, 0, 0, 3, 1, 0, 0, 2], [1349, 7, 1, 0, 0, 1, 0, 0, 0, 0, 0, 4, 0, 1, 2, 1], [1348, 18, 3, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0], [1347, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 2, 0, 1], [1346, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 3, 1, 3], [1345, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 0, 3, 1], [1344, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0], [1343, 19, 3, 1, 0, 0, 2, 0, 0, 0, 0, 9, 5, 1, 1, 2], [1342, 18, 2, 1, 0, 0, 1, 0, 0, 0, 0, 9, 4, 2, 1, 2], [1341, 19, 1, 0, 0, 0, 0, 0, 1, 0, 0, 5, 2, 0, 2, 1], [1340, 12, 2, 0, 0, 0, 0, 0, 1, 1, 0, 6, 3, 1, 1, 1], [1339, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 2, 3, 2], [1338, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 2, 0, 2], [1337, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 4, 0, 1, 0], [1336, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 2, 1, 3], [1335, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 2, 0, 0, 1], [1334, 6, 3, 0, 0, 1, 0, 1, 0, 0, 1, 2, 2, 0, 0, 0], [1333, 7, 3, 0, 0, 1, 0, 2, 0, 0, 0, 10, 1, 2, 6, 1], [1332, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 0, 3, 3], [1331, 17, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0], [1330, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2], [1329, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 2, 0, 4], [1328, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 3, 3, 0], [1327, 13, 2, 1, 1, 0, 0, 0, 0, 0, 0, 6, 2, 1, 2, 1], [1326, 24, 1, 0, 0, 1, 0, 0, 0, 0, 0, 6, 2, 1, 3, 0], [1325, 21, 1, 0, 0, 1, 0, 0, 0, 0, 0, 4, 2, 0, 2, 0], [1324, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2], [1323, 9, 1, 0, 1, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 3], [1322, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 1, 2, 0], [1321, 23, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [1320, 12, 1, 0, 0, 0, 0, 0, 1, 0, 0, 4, 2, 1, 0, 1], [1319, 24, 1, 1, 0, 0, 0, 0, 0, 0, 0, 7, 3, 3, 1, 0], [1318, 22, 3, 0, 0, 0, 0, 0, 1, 0, 2, 1, 0, 0, 1, 0], [1317, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 1, 0], [1316, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 1, 0, 1], [1315, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 2, 2, 0], [1314, 21, 2, 0, 1, 0, 1, 0, 0, 0, 0, 6, 0, 1, 3, 2], [1313, 7, 3, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0], [1312, 22, 3, 0, 0, 0, 0, 2, 1, 0, 0, 7, 3, 0, 0, 4], [1311, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0], [1310, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1309, 22, 3, 0, 1, 0, 0, 1, 0, 0, 1, 8, 4, 3, 1, 0], [1308, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 1, 0, 0], [1307, 21, 3, 0, 0, 1, 0, 1, 0, 1, 0, 10, 1, 1, 5, 3], [1306, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 1, 3, 2, 2], [1305, 6, 2, 0, 0, 1, 1, 0, 0, 0, 0, 9, 0, 2, 3, 4], [1304, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 1, 2, 4], [1303, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0]]]''';
  }

  static getDummyBillingData() async {
    await Future.delayed(const Duration(seconds: 1));

    // return '''[["Building","  Flat  ","Usage"," #AL ","!Al0","!Al1","!Al2","!Al3","!Al4","!Al5","!Al6","!Al7"," #ST ","!St1","!St2","!St3","!St7","US:00-02","US:02-04","US:04-06","US:06-08","US:08-10","US:10-12","US:12-14","US:14-16","US:16-18","US:18-20","US:20-22","US:22-24"],[["Gitaneel Arcade","5\u00266 Floor",112269,104,0,0,60,6,38,0,0,0,0,0,0,0,0,949,967,3512,9548,11905,12872,13460,12965,16881,18583,9453,1174],["Gitaneel Arcade","3rd Floor",9313,0,0,0,0,0,0,0,0,0,0,0,0,0,0,26,15,218,795,875,516,526,1252,1554,1799,1577,160],["Gitaneel Arcade","Ground Floor",89808,38,0,0,0,10,28,0,0,0,0,0,0,0,0,1889,1879,1987,5005,10433,10753,9753,10507,10049,8578,8831,4114],["Gitaneel Arcade","1st Floor",80208,30,0,0,0,0,30,0,0,0,0,0,0,0,0,6173,5628,7931,7962,6581,6861,7283,7371,6147,5411,5917,6943],["Gitaneel Arcade","Basement",41626,11,0,0,0,0,11,0,0,0,0,0,0,0,0,515,503,674,7165,10860,2361,4869,4834,3581,3711,1991,562]]]''';

    return '''[
  [ "Building", "Flat", "Usage", "#Al", "!Al0", "!Al1", "!Al2", "!Al3", "!Al4", "!Al5", "!Al6", "!Al7", "#Status", "!St1", "!St2", "!St3", "!St7" ],
  [
    [ "Lakeside Residency", "101",  12000, 2, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0 ],
    [ "Lakeside Residency", "102",  11000, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0 ],
    [ "Lakeside Residency", "103",  15000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ],
    [ "Oak Towers", "201",  18000, 3, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0 ],
    [ "Oak Towers", "202",  19000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ],
    [ "Pine Apartments", "301",  9500, 2, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1 ],
    [ "Pine Apartments", "302",  8700, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0 ],
    [ "Cedar Heights", "401",  23000, 5, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0 ],
    [ "Cedar Heights", "402",  22000, 3, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0 ],
    [ "Willow Estates", "501",  14000, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0 ],
    [ "Willow Estates", "502",  13500, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1 ],
    [ "Spruce Residences", "601",  20000, 4, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0 ],
    [ "Spruce Residences", "602",  21000, 2, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0 ],
    [ "Elm Grove", "701",  16000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ],
    [ "Elm Grove", "702",  15500, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0 ]
  ]
]''';
  }

  static Future<dynamic> getFilters({required String project}) async {
    String body = "00$project>Water Metering";
    var response;
    if (project.toLowerCase() == 'test') {
      response = await getDummyFilters();
    } else {
      response = await _makeRequest(
        body,
        url: wm1Url,
      );
    }

    return jsonDecode(response);
  }

  static Future<dynamic> getChartData(
      {required String project, required List<String> selectedLevels}) async {
    String body = "01$project>Water Metering|${selectedLevels.join('>')}";
    var response;
    if (project.toLowerCase() == 'test') {
      response = await getDummyChartData();
    } else {
      response = await _makeRequest(
        body,
        url: wm1Url,
      );
    }

    return jsonDecode(response);
  }

  static Future<dynamic> getBillingData(
      {required String project, required int monthNumber}) async {
    String body = "02$project>Water Metering|$monthNumber";
    var response;

    if (project.toLowerCase() == 'test') {
      response = await getDummyBillingData();
    } else {
      response = await _makeRequest(body, url: wm1Url);
    }
    // final response =
    //     await _makeRequest(body, url: wm1Url, contenttype: 'application/json');
    return jsonDecode(response);
  }

  static Future<dynamic> setBillingFormula(
      {required String project, required String formulaString}) async {
    String body = "03$project>Water Metering|$formulaString";
    final response = await _makeRequest(
      body,
      url: wm1Url,
    );
    return jsonDecode(response);
  }

  static Future<String> _makeRequest(String requestBody,
      {String url = wm1Url, Duration? timeout}) async {
    DateTime now = DateTime.now();

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw CustomException('No internet connection');
    }
    final jwt = await AuthService().getValidAccessToken();
    String userAgent = await DeviceInfoUtil.getUserAgent();
    final headers = {
      'User-Agent': userAgent,
      'medium': 'phone',
      'Content-Type': 'text/plain',
      'Authorization': 'Bearer $jwt',
    };
    var request = http.Request('POST', Uri.parse(url));
    request.body = requestBody;
    request.headers.addAll(headers);

    if (kDebugMode) {
      print("url ${request.url}");
      print("body ${request.body}");
      print("header ${request.headers}");
    }

    try {
      http.StreamedResponse response =
          await request.send().timeout(timeout ?? const Duration(seconds: 5));
      DateTime later = DateTime.now();

      if (kDebugMode) {
        print("Time taken: ${later.difference(now).inMilliseconds} ms");
        print(response.statusCode);
      }

      if (response.statusCode == 200) {
        var resp = await response.stream.bytesToString();
        if (kDebugMode) {
          print(resp);
        }
        return resp;
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        await AuthService().clearAuthData();
        throw CustomException('Redirecting to login page..Please login again');
      } else {
        String responseBody = await response.stream.bytesToString();
        if (kDebugMode) {
          print(responseBody);
        }
        throw CustomException(responseBody);
      }
    } on TimeoutException {
      throw CustomException('Request timed out');
    }
  }
}
